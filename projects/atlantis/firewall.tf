resource "google_compute_firewall" "healthcheck" {
  project     = module.project.id
  name        = "allow-${local.service_name}-healthcheck-traffic"
  network     = module.main_vpc.network_name
  target_tags = [local.vm_primary_network_tag]

  allow {
    protocol = "tcp"
    ports    = [local.service_listening_port]
  }

  # see https://cloud.google.com/load-balancing/docs/health-checks
  source_ranges = [
    "35.191.0.0/16",
    "130.211.0.0/22",
  ]
}

resource "google_compute_firewall" "internal_traffic" {
  project  = module.project.id
  name     = "allow-internal"
  network  = module.main_vpc.network_name
  priority = 65534

  allow {
    protocol = "tcp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "udp"
    ports    = ["0-65535"]
  }

  allow {
    protocol = "icmp"
  }

  # see https://cloud.google.com/load-balancing/docs/health-checks
  source_ranges = [
    "10.128.0.0/9",
  ]
}
