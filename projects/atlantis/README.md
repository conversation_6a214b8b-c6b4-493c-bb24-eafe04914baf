# Atlantis Runners  <!-- omit in toc -->

<details open="open">
<summary>Table of Contents</summary>

- [Deployment Requirements](#deployment-requirements)
- [Apply a Configuration Change](#apply-a-configuration-change)
- [Atlantis Bot Settings](#atlantis-bot-settings)
- [Runner to Environment Mapping](#runner-to-environment-mapping)

</details>

## Deployment Requirements

1. The atlantis legacy service account created in operations-core is needed for the runners.
2. A Slack app is needed and can be (re)created with the [manifest provided](./app-manifest.yml).
3. The Atlantis secrets can be loaded with the secret provisioner, naming them as in the [secret template](./secrets.tf).
   See how to create the `config.enc.yaml` [here](https://github.com/cat-home-experts/terraform-modules/tree/main/gcp/secret_manager/store#usage).

## Apply a Configuration Change

1. Duplicate the `tf_runner` module and apply then test.
   > Make sure you've incremented the module name and its `name` attribute

   <small>* or simply delete the current VM manually and apply your Terraform changes</small>
2. If all good, you can attach the new `tf_runner` module to the `tf_runner_instance_group` and apply.
   > ⚠️ The attachment of a new VM can take up to 7 minutes.

## Atlantis Bot Settings

| Characteristics ||
|-|-|
| Github username | checkatrade-github-bot |
| Github org memberships | cat-home-experts |
| Token scopes | repo:all, read:org, write:repo_hook, read:repo_hook, read:discussion |
| Allowlist repos permissions | Admin <small>*needed for creating the webhook</small> |

> [More details here](https://www.runatlantis.io/docs/access-credentials.html#github-user)

## [Runner to Environment Mapping](../../.github/pr_atlantis_release_help.md#runner-to-environment-mapping)
