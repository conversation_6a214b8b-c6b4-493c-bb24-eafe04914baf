locals {
  deny_policies_critical_exceptions = [
    "principal://iam.googleapis.com/projects/-/serviceAccounts/${local.operations_super_sa_email}",
    "principal://iam.googleapis.com/projects/-/serviceAccounts/${google_service_account.atlantis.email}",
    "principalSet://goog/group/<EMAIL>",
    "principalSet://goog/group/<EMAIL>",
  ]
}

resource "google_iam_deny_policy" "secure_project" {
  provider = google-beta

  parent       = urlencode("cloudresourcemanager.googleapis.com/projects/${module.project.id}")
  name         = "secure-project-access"
  display_name = "Remove permissive inherited permissions"

  rules {
    description = "Deny inherited permissions giving access to the VM custom metadata"
    deny_rule {
      denied_principals = [
        #"principalSet://goog/public:all",
        "principalSet://goog/group/<EMAIL>",
      ]
      denied_permissions   = ["compute.googleapis.com/instances.get"]
      exception_principals = local.deny_policies_critical_exceptions
    }
  }

  rules {
    description = "Deny inherited permissions giving access to IAM Service Accounts"
    deny_rule {
      denied_principals = [
        "principalSet://goog/group/<EMAIL>",
      ]
      denied_permissions = [
        "iam.googleapis.com/serviceAccountKeys.get",
        "iam.googleapis.com/serviceAccounts.get",
      ]
      exception_principals = local.deny_policies_critical_exceptions
    }
  }

  rules {
    description = "Deny inherited permissions giving access to the Org policies to make sure no one unauthorised get access to their conditions"
    deny_rule {
      denied_principals = [
        "principalSet://goog/group/<EMAIL>",
      ]
      denied_permissions   = ["orgpolicy.googleapis.com/policy.get"]
      exception_principals = local.deny_policies_critical_exceptions
    }
  }
}
