locals {
  # fqdn should render: [env].atlantis.gcp.cathex.io
  fqdn = "${module.project.env}.${local.service_name}.${local.dns_zone}.cathex.io"

  dns_zone            = "gcp"
  instance_group_zone = "${var.region}-a"

  service_git_repo_name  = "gcp-atlantis"
  service_listening_port = 4141
  service_name           = "atlantis"
  atlantis_url           = "https://${local.fqdn}"

  maintenance_notification_slack_channel = "#sre-private"

  docker_tag_map = {
    development = "latest"
    staging     = "latest"
    production  = "latest"
  }
  container_image = "${var.region}-docker.pkg.dev/${module.project.id}/gcp-atlantis/atlantis:${local.docker_tag_map[module.project.environment]}"

  container_env_vars = merge(
    var.atlantis_settings,
    {
      ATLANTIS_ATLANTIS_URL = local.atlantis_url

      # Removing ATLANTIS_DEFAULT_TF_VERSION will let Atlantis follow the comparison or
      # pessimistic version constraint specified in main.tf.
      # See https://www.runatlantis.io/docs/terraform-versions.html#via-terraform-config.
      ATLANTIS_DEFAULT_TF_VERSION = "v1.0.11"

      ATLANTIS_ENABLE_REGEXP_CMD            = "true"
      ATLANTIS_GH_USER                      = data.google_secret_manager_secret_version.secrets_latest["github-cat-home-experts-username"].secret_data
      ATLANTIS_GH_TOKEN                     = data.google_secret_manager_secret_version.secrets_latest["github-cat-home-experts-bot-token"].secret_data
      ATLANTIS_GH_WEBHOOK_SECRET            = data.google_secret_manager_secret_version.secrets_latest["github-webhook-secret"].secret_data
      ATLANTIS_HIDE_PREV_PLAN_COMMENTS      = "false" # does not work very well with multiple runners, so had to disable it
      ATLANTIS_HIDE_UNCHANGED_PLAN_COMMENTS = "true"
      ATLANTIS_PORT                         = local.service_listening_port
      ATLANTIS_QUIET_POLICY_CHECKS          = "true"
      ATLANTIS_REPO_CONFIG                  = "/home/<USER>/repos.yaml"
      ATLANTIS_SILENCE_ALLOWLIST_ERRORS     = "false"
      ATLANTIS_SILENCE_NO_PROJECTS          = "true" # needed for multi-runner on mono-repo
      ATLANTIS_SILENCE_VCS_STATUS_NO_PLANS  = "false"
      ATLANTIS_SKIP_CLONE_NO_CHANGES        = "true"
      ATLANTIS_WEB_BASIC_AUTH               = "true"
      ATLANTIS_WEB_USERNAME                 = "atlantis"
      ATLANTIS_WEB_PASSWORD                 = data.google_secret_manager_secret_version.secrets_latest["atlantis-webservice-password"].secret_data
      ATLANTIS_WEBSOCKET_CHECK_ORIGIN       = "true"
      ATLANTIS_WRITE_GIT_CREDS              = "true"
      ARM_CLIENT_ID_FOR_DEV                 = data.google_secret_manager_secret_version.secrets_latest["arm-client-id-for-dev"].secret_data
      ARM_CLIENT_SECRET_FOR_DEV             = data.google_secret_manager_secret_version.secrets_latest["arm-client-secret-for-dev"].secret_data
      ARM_TENANT_ID_FOR_DEV                 = data.google_secret_manager_secret_version.secrets_latest["arm-tenant-id-for-dev"].secret_data
      ARM_CLIENT_ID_FOR_PROD                = data.google_secret_manager_secret_version.secrets_latest["arm-client-id-for-prod"].secret_data
      ARM_CLIENT_SECRET_FOR_PROD            = data.google_secret_manager_secret_version.secrets_latest["arm-client-secret-for-prod"].secret_data
      ARM_TENANT_ID_FOR_PROD                = data.google_secret_manager_secret_version.secrets_latest["arm-tenant-id-for-prod"].secret_data
      AWS_ACCESS_KEY_ID                     = data.google_secret_manager_secret_version.secrets_latest["aws-access-key-id"].secret_data
      AWS_SECRET_ACCESS_KEY                 = data.google_secret_manager_secret_version.secrets_latest["aws-secret-access-key"].secret_data
      CONFTEST_POLICIES_REPO_URI            = "github.com/cat-home-experts/conftest-policies"
      DEFAULT_CONFTEST_VERSION              = "0.42.1"
      SNYK_ORG                              = "************************************"
      SNYK_TOKEN                            = data.google_secret_manager_secret_version.secrets_latest["snyk-token"].secret_data
    },
  )

  vm_primary_network_tag = "container-vm-${local.service_name}"

  # Reasoning behind the Systemd timer:
  # According to that documentaiton (https://cloud.google.com/compute/docs/metadata/getting-live-migration-notice) GCP updates 
  # Metadata server maintenance event 60 seconds prior to a maintenance. So a timer checking the Metadata server every 59 secs
  # would warns us on time about any migration, no matter when you deployed your VM.
  # Container-Optimized OS file system is documented here: https://cloud.google.com/container-optimized-os/docs/concepts/disks-and-filesystem
  vm_startup_script = <<-EOT
  echo 'alias l="ls -lah"
alias open-atlantis="docker exec -it -w /home/<USER>/bin/bash"
' > /etc/profile.d/checkatrade-shell-aliases.sh

  echo -e 'echo "\n\033[3;34m  To connect to the Atlantis container, execute: open-atlantis\033[0m\n"' > /etc/profile.d/checkatrade-welcome.sh

  sudo mkdir -p /etc/systemd/system/maintenance

  sudo cat >/etc/systemd/system/maintenance/notify-about-maintenance.sh <<EOL
  #!/usr/bin/env bash

  ACCESS_TOKEN="\$(curl \\
    http://metadata.google.internal/computeMetadata/v1/instance/service-accounts/default/token \\
    -H 'accept: application/json' \\
    -H 'Metadata-Flavor: Google' | jq -r '.access_token' | sed 's/\\.*\$//g')"

  SLACK_APP_BOT_TOKEN="\$(curl \\
    'https://secretmanager.googleapis.com/v1/projects/${module.project.id}/secrets/slack-app-bot-token/versions/latest:access' \\
    --header "Authorization: Bearer \$ACCESS_TOKEN" \\
    --header 'Accept: application/json' \\
    --compressed | jq -r '.payload.data' | tr '_-' '/+' | base64 -d)"

  METADATA_VALUE=\$(curl http://metadata.google.internal/computeMetadata/v1/instance/maintenance-event -H "Metadata-Flavor: Google")

  if [[ "\$METADATA_VALUE" != "NONE" ]]; then
    curl -H "Content-type: application/json" \\
      --data '{"channel":"${local.maintenance_notification_slack_channel}","blocks":[{"type":"section","text":{"type":"mrkdwn","text":"*GCP VM Live Migration Notice*"}},{"type":"divider"},{"type":"section","text":{"type":"mrkdwn","text":"GCP will initiate a ${module.project.environment} env Atlantis VM host maintenance in less than 60 seconds. The subsequent live migration might slightly degrade the VM performances but should not shut it down."},"accessory": {"type": "image","image_url": "https://www.runatlantis.io/hero.png","alt_text": "Atlantis ${module.project.env} runner"}}]}' \\
      -H "Authorization: Bearer \$SLACK_APP_BOT_TOKEN" \\
      -X POST https://slack.com/api/chat.postMessage
  fi
  EOL

  sudo chmod +x /etc/systemd/system/maintenance/notify-about-maintenance.sh

  sudo cat >/etc/systemd/system/maintenance.service <<EOL
  [Unit]
  Description=Send a Slack notification via the Atlantis app to keep posted about VM maintenances

  [Service]
  Type=oneshot
  ExecStart=/etc/systemd/system/maintenance/notify-about-maintenance.sh

  [Install]
  WantedBy=multi-user.target
  EOL

  sudo chmod 664 /etc/systemd/system/maintenance.service

  sudo cat >/etc/systemd/system/maintenance.timer <<EOL
  [Unit]
  Description=Run maintenance.service every 10 minutes

  [Timer]
  OnCalendar=*-*-* *:*:0/59
  EOL

  sudo systemctl start maintenance.timer
  sudo systemctl list-timers
  EOT
}
