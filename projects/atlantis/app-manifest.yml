# Documentation: https://api.slack.com/reference/manifests

_metadata:
  major_version: 1
  minor_version: 1
display_information:
  name: Atlantis
  description: An app with a bot token for the Atlantis runners to be able to send Slack messages
  background_color: "#3c9bd5"
features:
  bot_user:
    display_name: Atlantis
    always_online: false
  app_home:
    home_tab_enabled: false
    messages_tab_enabled: true
    messages_tab_read_only_enabled: true
oauth_config:
  scopes:
    bot:
      - chat:write
settings:
  interactivity:
    is_enabled: false
  org_deploy_enabled: false
  socket_mode_enabled: false
