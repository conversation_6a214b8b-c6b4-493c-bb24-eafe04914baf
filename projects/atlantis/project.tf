locals {
  project_configuration = {
    name   = var.project_name
    folder = var.environment
    apis = concat(var.apis, [
      "compute.googleapis.com", # Required for data.google_compute_network, google_compute_health_check
    ])
  }
}

module "project" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/project?ref=3.63.3"

  project_config = local.project_configuration
  team_email     = "<EMAIL>"
}
