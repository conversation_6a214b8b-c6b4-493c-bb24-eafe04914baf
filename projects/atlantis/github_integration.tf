data "external" "create_repo_webhooks" {
  for_each = {
    # The replace removes any whitespace then we split to obtain a list
    for repo in split(",", replace(var.atlantis_settings["ATLANTIS_REPO_ALLOWLIST"], "/\\s+/", "")) : repo => {
      repo_org  = split("/", repo)[1]
      repo_name = split("/", repo)[2]
    }
  }

  program = ["bash", "-c", <<-EOT
      curl -s -X POST \
        -H "Accept: application/vnd.github+json" \
        -H "Authorization: Bearer ${data.google_secret_manager_secret_version.secrets_latest["github-${each.value.repo_org}-bot-token"].secret_data}"\
        -H "X-GitHub-Api-Version: 2022-11-28" \
        https://api.github.com/repos/${each.value.repo_org}/${each.value.repo_name}/hooks \
        -d '{"active":true,"events":["issue_comment","pull_request","pull_request_review","pull_request_review_comment"],"config":{"url":"https://${local.fqdn}/events","content_type":"json","insecure_ssl":"1","secret":"${data.google_secret_manager_secret_version.secrets_latest["github-webhook-secret"].secret_data}"}}' \
        | jq '. | { "id": .id | tostring }'
    EOT
  ]
}
