module "atlantis_repo" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/artifact_registry?ref=3.62.1"

  project = module.project
  name    = local.service_git_repo_name

  registry_readers = [
    "serviceAccount:${google_service_account.atlantis.email}", # For the VM to be able to pull the image
  ]
  registry_writers = [
    "serviceAccount:${google_service_account.cloud_build.email}",
  ]
}
