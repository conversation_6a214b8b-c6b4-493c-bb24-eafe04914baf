resource "google_compute_health_check" "tf_runner_health_check" {
  project            = module.project.id
  name               = "${local.service_name}-health-check"
  timeout_sec        = 1
  check_interval_sec = 1

  tcp_health_check {
    port = local.service_listening_port
  }

  depends_on = [module.project.project_apis_wait]
}

resource "google_compute_backend_service" "tf_runner_backend_service" {
  project   = module.project.id
  name      = "${local.service_name}-backend-service"
  port_name = "http"
  protocol  = "HTTP"

  backend {
    group = module.tf_runner_instance_group.instance_group.id
  }

  health_checks = [google_compute_health_check.tf_runner_health_check.id]
}

module "tf_runner_load_balancer" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/load_balancer/http?ref=3.62.1"

  project              = module.project
  name                 = local.service_name
  backend_service_link = google_compute_backend_service.tf_runner_backend_service.self_link
  enable_ssl           = true
  enable_http          = false
  create_dns_entries   = false
  cert_name            = local.service_name
  cert_domains         = ["${local.fqdn}."]
}
