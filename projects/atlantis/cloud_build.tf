module "deployment_metrics_trigger" {
  source = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/deployment_metrics_trigger?ref=3.63.4"

  project = module.project
}

module "atlantis_image_triggers" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build?ref=3.69.1"

  for_each = var.atlantis_image_triggers

  project = module.project

  name                         = each.value.name
  description                  = each.value.description
  push_trigger_enabled         = each.value.push_trigger_enabled != null ? each.value.push_trigger_enabled : false
  pull_request_trigger_enabled = each.value.pull_request_trigger_enabled != null ? each.value.pull_request_trigger_enabled : false
  branch_regex                 = each.value.branch_regex
  invert_regex                 = each.value.invert_regex != null ? each.value.invert_regex : false
  excluded_files_filter = [
    ".github/**",
    ".linters/**",
    ".security/**",
    ".vscode/**",
    "miscellaneous/**",
    "scripts/*.sh",
    "tests/**",
    ".gitignore",
    ".gitmodules",
    ".pre-commit-config.yaml",
    "cloudbuild-kaniko.yaml",
    "cloudbuild.yaml",
    "Dockerfile.alpine",
    "readme.md",
    "README.md",
  ]
  owner                  = "cat-home-experts"
  repo_name              = local.service_git_repo_name
  filename               = "cloudbuild.yaml"
  create_service_account = false
  service_account_key    = trimsuffix(google_service_account.cloud_build.email, regex("@.*", google_service_account.cloud_build.email))
  env_variables = {
    _GCP_REGION            = var.region
    _KANIKO_CACHE_TTL      = "1440h"
    _OPERATIONS_PROJECT_ID = module.project.operations_project_id
    _SERVICE_NAME          = local.service_name
  }
}
