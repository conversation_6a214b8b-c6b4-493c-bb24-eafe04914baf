variable "project_name" {
  type = string
}

variable "environment" {
  type    = string
  default = ""
}

variable "region" {
  type        = string
  description = "(Optional) The region the instance will sit in."
  default     = "europe-west2"
}

variable "apis" {
  type    = list(string)
  default = []
}

variable "atlantis_image_triggers" {
  type = map(object({
    name                         = string
    description                  = string
    push_trigger_enabled         = optional(bool)
    pull_request_trigger_enabled = optional(bool)
    branch_regex                 = string
    invert_regex                 = optional(bool)
  }))
}

variable "atlantis_vm_machine_type" {
  type        = string
  description = <<-EOF
  The size/type of the VM.
  Note: If you want to update this value (resize the VM) after initial creation, you must set allow_stopping_for_update to true.
  Custom machine types can be formatted as custom-NUMBER_OF_CPUS-AMOUNT_OF_MEMORY_MB, e.g. custom-6-20480 for 6 vCPU and 20GB of RAM.
  There is a limit of 6.5 GB per CPU unless you add extended memory. You must do this explicitly by adding the suffix -ext, e.g. custom-2-15360-ext for 2 vCPU and 15 GB of memory.
  EOF
}

variable "atlantis_settings" {
  type        = map(string)
  description = "Environment variables of Atlantis documented here https://www.runatlantis.io/docs/server-configuration.html#environment-variables."
}
