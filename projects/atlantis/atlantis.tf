module "tf_runner_vm_01" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/compute/container_vm?ref=3.71.0"

  project          = module.project
  name             = "${local.service_name}-01"
  machine_type     = var.atlantis_vm_machine_type
  disk_type        = "pd-ssd"
  disk_auto_delete = true

  compute_image = {
    project_id = "cos-cloud"
    name       = "cos-stable-101-17162-40-42"
  }

  allow_stopping_for_update = true
  zone                      = local.instance_group_zone
  vm_startup_script         = local.vm_startup_script
  vm_network_tags           = [local.vm_primary_network_tag, "http-server", "https-server", ]
  network                   = module.main_vpc.network_self_link

  container_image          = local.container_image
  container_port           = local.service_listening_port
  container_env_vars       = local.container_env_vars
  container_restart_policy = "Always"

  service_account = google_service_account.atlantis.email

  depends_on = [
    module.project_access_permissions,
    google_service_account_iam_member.super_sa_s_impersonation,
    module.atlantis_repo,
  ]
}

module "tf_runner_instance_group" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/compute/instance_group?ref=3.75.2"

  project           = module.project
  zone              = local.instance_group_zone
  name              = local.service_name
  http_port         = local.service_listening_port
  network_self_link = module.main_vpc.network_self_link

  compute_instances = [
    module.tf_runner_vm_01.google_compute_instance.self_link,
  ]
}
