module "secrets_provisioner" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/store?ref=3.62.1"

  project = module.project

  secrets_keys = [
    { name = "atlantis-webservice-password" },
    { name = "github-cat-home-experts-username" },
    { name = "github-cat-home-experts-bot-token" },
    { name = "github-webhook-secret" },
    { name = "slack-app-bot-token" },
    { name = "aws-access-key-id" },
    { name = "aws-secret-access-key" },
    { name = "snyk-token" },
    { name = "arm-client-id-for-dev" },
    { name = "arm-client-secret-for-dev" },
    { name = "arm-tenant-id-for-dev" },
    { name = "arm-client-id-for-prod" },
    { name = "arm-client-secret-for-prod" },
    { name = "arm-tenant-id-for-prod" },
  ]

  provisioner_enabled = true
}

data "google_secret_manager_secret_version" "secrets_latest" {
  for_each = toset(module.secrets_provisioner.secret_names)

  project = module.project.id
  secret  = each.key

  depends_on = [module.secrets_provisioner]
}
