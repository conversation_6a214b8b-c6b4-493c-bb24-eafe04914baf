locals {
  operations_super_sa_email = "atlantis@${module.project.operations_project_id}.iam.gserviceaccount.com"
  lacework_super_sa_email   = "lacework-deployer@${module.project.operations_project_id}.iam.gserviceaccount.com"
}

data "google_secret_manager_secret_version" "platform_builders_pullers_group_id" {
  project = module.project.operations_project_id
  secret  = "platform-builders-pullers-group-id"
}

# This principal referenced below will be allowed to pull images from the Platform Builder Artifact Registry repo
module "builders_pullers_group_updater" {
  source = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/gcp_cloud_identity/sa_group_membership?ref=3.62.1"

  project   = module.project
  group_id  = data.google_secret_manager_secret_version.platform_builders_pullers_group_id.secret_data
  principal = google_service_account.cloud_build.email
}

resource "google_service_account_iam_member" "super_sa_s_impersonation" {
  for_each = toset([local.operations_super_sa_email, local.lacework_super_sa_email])

  service_account_id = "projects/${module.project.operations_project_id}/serviceAccounts/${each.key}"
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${google_service_account.atlantis.email}"
}

module "project_access_permissions" {
  source = "git::**************:cat-home-experts/terraform-modules.git//gcp/iam/project_authoritative?ref=2.0.0"

  project = module.project

  machines = {
    "roles/iam.serviceAccountUser" : [
      "serviceAccount:${google_service_account.cloud_build.email}",
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]
    "roles/iam.serviceAccountTokenCreator" : [
      # For the image cleanup step which needs to use a print-access-token
      "serviceAccount:${google_service_account.cloud_build.email}",
    ]
    "roles/cloudfunctions.developer" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
    ]
    "roles/secretmanager.secretAccessor" : [
      "serviceAccount:${module.deployment_metrics_trigger.cloud_build_sa_email}",
      "serviceAccount:${module.project.id}@appspot.gserviceaccount.com", # Cloud Functions 1st gen default SA for gcp-cloud-build-metrics
    ]
  }
}

data "google_compute_default_service_account" "this" {
  project = module.project.id

  depends_on = [module.project.project_apis_wait]
}

# Authorises the VM maintenance.service to get the slack app bot token from Secret Manager
resource "google_secret_manager_secret_iam_binding" "slack_app_bot_token" {
  project   = module.project.number
  secret_id = "projects/${module.project.number}/secrets/slack-app-bot-token"
  role      = "roles/secretmanager.secretAccessor"
  members   = ["serviceAccount:${local.operations_super_sa_email}"]

  depends_on = [module.secrets_provisioner]
}
