variable "project_name" {
  type    = string
  default = ""
}

variable "environment" {
  type    = string
  default = ""
}

variable "region" {
  type        = string
  description = "(Optional) The region the project will sit in."
  default     = "europe-west2"
}

variable "project_static_permissions" {
  type        = map(list(string))
  description = "A map of roles to their list of IAM groups."
  default     = {}
}

variable "project_team" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "project_team_admin" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "qa_team" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "qa_team_admin" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "viewers" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "everyone" {
  type        = list(string)
  description = "A list of IAM users ('user:email') and groups ('group:email')."
  default     = []
}

variable "incident_io" {
  type        = string
  description = "(Optional) Name of IncidentIO group to alert for datadog monitors"
  default     = ""
}

variable "monitor_tags" {
  type        = list(string)
  description = "Default tags to add to monitors"
  default     = ["team:media"]
}

variable "media_team_slack" {
  type        = string
  description = "Media team slack channel to alert for datadog monitors"
  default     = ""
}
