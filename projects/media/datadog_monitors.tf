# Media Team Datadog Monitors for Sponsored Listings
locals {
  map_environment_to_env = tomap({
    development = "dev"
    staging     = "stg"
    production  = "prod"
  })

  env = local.map_environment_to_env[var.environment]

  # Base message for all monitors
  message_text  = "please investigate\n{{log.link}}\n${var.media_team_slack}\n"
  message_alert = "${local.message_text}{{#is_alert}}${var.incident_io}{{/is_alert}}{{#is_alert_recovery}}${var.incident_io}{{/is_alert_recovery}}"

  # Sponsored Listings specific monitors
  sponsored_listings_monitors = {
    sponsored_listings_new_errors = {
      name     = "Sponsored Listings - New Error Issues"
      type     = "error_tracking"
      message  = "New error detected in Sponsored Listings module. ${local.message_alert}"
      query    = "error-tracking(\"@tags.module:SponsoredListings\").source(\"browser\").new().rollup(\"count\").by(\"@issue.id\").last(\"1d\") > 0"
      
      critical_threshold = "0"
      critical_recovery  = "0"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "error-tracking", "${var.environment}"])
      priority          = 2
    },

    sponsored_listings_high_impact_errors = {
      name     = "Sponsored Listings - High Impact Error Issues"
      type     = "error_tracking"
      message  = "High impact error detected in Sponsored Listings module affecting multiple users. ${local.message_alert}"
      query    = "error-tracking(\"@tags.module:SponsoredListings\").source(\"browser\").impact().rollup(\"count\").by(\"@issue.id\").last(\"1d\") > 10"
      
      critical_threshold = "10"
      critical_recovery  = "9"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "error-tracking", "high-impact", "${var.environment}"])
      priority          = 1
    },

    sponsored_listings_campaign_fetch_errors = {
      name     = "Sponsored Listings - Campaign Fetch Errors"
      type     = "error_tracking"
      message  = "Campaign data fetch errors detected in Sponsored Listings. ${local.message_alert}"
      query    = "error-tracking(\"@tags.module:SponsoredListings AND @tags.method:useSponsoredListingCampaigns\").source(\"browser\").new().rollup(\"count\").by(\"@issue.id\").last(\"1d\") > 0"
      
      critical_threshold = "0"
      critical_recovery  = "0"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "campaign-fetch", "${var.environment}"])
      priority          = 2
    },

    sponsored_listings_stats_fetch_errors = {
      name     = "Sponsored Listings - Stats Fetch Errors"
      type     = "error_tracking"
      message  = "Campaign statistics fetch errors detected in Sponsored Listings. ${local.message_alert}"
      query    = "error-tracking(\"@tags.module:SponsoredListings AND @tags.method:useGetCampaignStatsForAllPeriods\").source(\"browser\").new().rollup(\"count\").by(\"@issue.id\").last(\"1d\") > 0"
      
      critical_threshold = "0"
      critical_recovery  = "0"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "stats-fetch", "${var.environment}"])
      priority          = 2
    }
  }

  sponsored_listings_rum_monitors = {
    sponsored_listings_rum_errors = {
      name     = "Sponsored Listings - RUM Error Count"
      type     = "rum alert"
      message  = "High error count detected in Sponsored Listings RUM data. ${local.message_alert}"
      query    = "rum(\"@type:error @context.tags.module:SponsoredListings\").rollup(\"count\").last(\"15m\") > 5"

      critical_threshold = "5"
      critical_recovery  = "4"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "rum", "${var.environment}"])
      priority          = 2
    },

    sponsored_listings_rum_error_rate = {
      name     = "Sponsored Listings - RUM Error Rate"
      type     = "rum alert"
      message  = "High error rate detected in Sponsored Listings. ${local.message_alert}"
      query    = "rum(\"@type:error @context.tags.module:SponsoredListings\").rollup(\"count\").last(\"1h\") > 10"

      critical_threshold = "10"
      critical_recovery  = "9"
      evaluation_delay   = 0
      tags              = concat(var.monitor_tags, ["sponsored-listings", "rum", "error-rate", "${var.environment}"])
      priority          = 2
    }
  }

  all_monitors = merge(local.sponsored_listings_monitors, local.sponsored_listings_rum_monitors)

  filtered_monitors = {
    for key, value in local.all_monitors : key => value
    if local.env == "prod" || (try(value.prod_only, false) == false && local.env != "prod")
  }
}

module "sponsored_listings_error_monitors" {
  for_each = local.filtered_monitors

  source  = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/datadog/monitors?ref=3.78.0"
  name    = "${var.environment} - ${each.value.name}"
  type    = each.value.type
  message = each.value.message
  query   = each.value.query

  critical_threshold  = each.value.critical_threshold
  critical_recovery   = each.value.critical_recovery
  require_full_window = false
  notify_no_data      = false
  evaluation_delay    = each.value.evaluation_delay
  tags                = each.value.tags
  timeout_h           = 0
  priority            = each.value.priority
}
