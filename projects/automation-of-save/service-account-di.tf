locals {
  di_service_account = {
    name = "di-sa-integration"
  }
}

resource "google_service_account" "di_service_account" {
  count = 1

  project = module.project.id

  account_id   = local.di_service_account.name
  display_name = "Service account for pubsub publishing outside of GCP project"
}

resource "google_service_account_key" "di_service_account_key" {
  service_account_id = google_service_account.di_service_account[0].email
}

module "di_sa_secrets" {
  source      = "git::**************:cat-home-experts/terraform-modules.git//gcp/secret_manager/store?ref=1.0.7"
  project_id  = module.project.id
  secret_id   = "${local.di_service_account.name}-json-key"
  secret_data = base64decode(google_service_account_key.di_service_account_key.private_key)
}