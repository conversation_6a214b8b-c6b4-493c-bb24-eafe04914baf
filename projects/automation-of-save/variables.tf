variable "project_name" {
  type = string
}

variable "project_folder" {
  type = string
}

variable "region" {
  type        = string
  description = ""
  default     = "europe-west2"
}

variable "apis" {
  type    = list(string)
  default = []
}

variable "environment" {
  type    = string
  default = ""
}

variable "viewer_access" {
  type        = list(string)
  description = "A list of account (group) emails to which viewer access will be given."
  default     = []
}

# ▽▽ not used so commented for now
# variable "developer_access" {
#   type        = list(string)
#   description = "A list of account (group) emails to which developer (invoker) access will be given."
#   default     = []
# }

variable "team_access" {
  type        = list(string)
  default     = []
  description = "A list of team accounts (groups) to which general team access will be given."
}

variable "publisher_access" {
  type        = list(string)
  default     = []
  description = "A list of team accounts (groups) to which publisher access will be given."
}

variable "cloudbuild_triggers" {
  type = map(object({
    name                         = string
    description                  = string
    disabled                     = bool
    push_trigger_enabled         = bool
    pull_request_trigger_enabled = bool
    owner                        = string
    repo_name                    = string
    branch_regex                 = string
    invert_regex                 = bool
    comment_control              = string
    filename                     = string
    env_variables                = map(string)
    included_files_filter        = optional(list(string))
    excluded_files_filter        = optional(list(string))
  }))
}

variable "cloud_run_env_variables" {
  type = map(list(any))
}

variable "cloud_run_parameters" {
  type = map(object({
    cpu                   = string
    memory                = string
    max_scale             = number
    min_scale             = number
    initial_scale         = number
    container_concurrency = number
  }))
}

# Pubsub alert variables
variable "alert_duration" {
  type        = string
  description = "Length of time in seconds that an alert threshold remains breached to be considered failing - 0s, 60s, 120s or 300s accepted values"
}

variable "threshold_value" {
  type        = number
  description = "Number of unacked messages before threshold is considered breached"
}

variable "trigger_count" {
  type        = number
  description = "Number of times the threshold must be breached before triggering the alert"
}