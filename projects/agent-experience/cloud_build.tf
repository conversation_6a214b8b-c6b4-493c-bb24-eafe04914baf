# resource "google_service_account" "cloud_build" {
#   project      = module.project.id
#   account_id   = "build-${var.project_name}"
#   display_name = "build-${var.project_name}"
# }


# If you want Cloud Build statuses sent to Slack uncomment the slackbot_trigger module call below, otherwise
# you can remove it
# Warning! If you do take this module manual work (by the SRE team) is required to create the necessary webhooks

module "slackbot_trigger" {
  source  = "git::**************:cat-home-experts/terraform-modules.git//gcp/slackbot_trigger?ref=3.90.0"
  project = module.project
  env_variables = {
    _GCP_REGION   = var.region
    _TRIGGER_NAME = "slack-notifier"
  }
}

module "deployment_metrics_trigger" {
  source = "git::**************:cat-home-experts/terraform-modules.git//checkatrade/deployment_metrics_trigger?ref=3.90.0"

  project = module.project
}

# module "cloud_build_triggers" {
#   source = "git::**************:cat-home-experts/terraform-modules.git//gcp/cloud_build_v2?ref=3.69.1"

#   for_each = var.cloud_build_triggers

#   project                      = module.project
#   description                  = each.value["description"]
#   name                         = each.value["name"]
#   create_service_account       = false
#   service_account_key          = google_service_account.cloud_build.account_id
#   env_variables                = each.value["env_variables"]
#   disabled                     = each.value["disabled"]
#   push_trigger_enabled         = each.value["push_trigger_enabled"]
#   pull_request_trigger_enabled = each.value["pull_request_trigger_enabled"]
#   owner                        = each.value["owner"]
#   repo_name                    = each.value["repo_name"]
#   branch_regex                 = each.value["branch_regex"]
#   invert_regex                 = each.value["invert_regex"]
#   filename                     = each.value["filename"]
#   included_files_filter        = each.value["included_files_filter"]
#   excluded_files_filter        = each.value["excluded_files_filter"]
# }
